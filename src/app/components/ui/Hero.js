'use client';
import { getTranslation } from '@/lib/i18n';
import { <PERSON><PERSON>, Card, CardHeader, CardBody, CardFooter, Link } from '@heroui/react';
import clsx from 'clsx';
import { useState, useEffect, useCallback, use } from 'react';


export default function Hero({ locale = 'en', color = 'black' }) {
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [isHovered, setIsHovered] = useState(false);
  const [showFullscreenTip, setShowFullscreenTip] = useState(false);

  const lang = locale === 'en' ? '' : locale;

  const t = function (key) {
    return getTranslation(locale, key);
  }

  const colorDatas = {
    black: {
      title: t('Black Screen Tool'),
      colorValue: "#000000",
      label: t('Black'),
      labelTextColor: "text-white",
      path: `${lang}/`,
    },
    white: {
      title: t('White Screen Tool'),
      colorValue: "#ffffff",
      label: t('White'),
      labelTextColor: "text-black",
      path: `${lang}/white-screen`,
    },
    red: {
      title: t('Red Screen Tool'),
      colorValue: "#ff0000",
      label: t('Red'),
      labelTextColor: "text-white",
      path: `${lang}/red-screen`,
    },
    green: {
      title: t('Green Screen Tool'),
      colorValue: "#00ff00",
      label: t('Green'),
      labelTextColor: "text-white",
      path: `${lang}/green-screen`,
    },
    blue: {
      title: t('Blue Screen Tool'),
      colorValue: "#0000ff",
      label: t('Blue'),
      labelTextColor: "text-white",
      path: `${lang}/blue-screen`,
    },
    yellow: {
      title: t('Yellow Screen Tool'),
      colorValue: "#ffff00",
      label: t('Yellow'),
      labelTextColor: "text-black",
      path: `${lang}/yellow-screen`,
    },
    orange: {
      title: t('Orange Screen Tool'),
      colorValue: "#FFA500",
      label: t('Orange'),
      labelTextColor: "text-white",
      path: `${lang}/orange-screen`,
    },
    pink: {
      title: t('Pink Screen Tool'),
      colorValue: "#FF69B4",
      label: t('Pink'),
      labelTextColor: "text-white",
      path: `${lang}/pink-screen`,
    },
    purple: {
      title: t('Purple Screen Tool'),
      colorValue: "#800080",
      label: t('Purple'),
      labelTextColor: "text-white",
      path: `${lang}/purple-screen`,
    },
  };

  const { title } = colorDatas[color];
  const [colorValue, setColorValue] = useState(colorDatas[color].colorValue);

  // 根据背景颜色计算合适的文字颜色
  const getTextColor = (bgColor) => {
    // 将十六进制颜色转换为RGB
    const hex = bgColor.replace('#', '');
    const r = parseInt(hex.substr(0, 2), 16);
    const g = parseInt(hex.substr(2, 2), 16);
    const b = parseInt(hex.substr(4, 2), 16);

    // 计算亮度 (使用相对亮度公式)
    const brightness = (r * 299 + g * 587 + b * 114) / 1000;

    // 如果背景较亮，使用深色文字；如果背景较暗，使用浅色文字
    return brightness > 128 ? 'rgba(0, 0, 0, 0.8)' : 'rgba(255, 255, 255, 0.8)';
  };

  // 处理颜色输入变化
  const handleColorInputChange = (value) => {
    setColorValue(value);
  };

  // 进入全屏模式
  const enterFullscreen = useCallback(() => {
    if (document.documentElement.requestFullscreen) {
      document.documentElement.requestFullscreen();
    } else if (document.documentElement.webkitRequestFullscreen) {
      document.documentElement.webkitRequestFullscreen();
    } else if (document.documentElement.msRequestFullscreen) {
      document.documentElement.msRequestFullscreen();
    }
    setIsFullscreen(true);
    setShowFullscreenTip(true);

    // 3秒后隐藏提示
    setTimeout(() => {
      setShowFullscreenTip(false);
    }, 3000);
  }, []);

  // 退出全屏模式
  const exitFullscreen = useCallback(() => {
    if (document.exitFullscreen) {
      document.exitFullscreen();
    } else if (document.webkitExitFullscreen) {
      document.webkitExitFullscreen();
    } else if (document.msExitFullscreen) {
      document.msExitFullscreen();
    }
    setIsFullscreen(false);
  }, []);

  // 切换全屏模式
  const toggleFullscreen = useCallback(() => {
    if (isFullscreen) {
      exitFullscreen();
    } else {
      enterFullscreen();
    }
  }, [isFullscreen, enterFullscreen, exitFullscreen]);

  // 键盘事件处理
  useEffect(() => {
    const handleKeyDown = (event) => {
      switch (event.key) {
        case 'f':
        case 'F':
          event.preventDefault();
          if (!isFullscreen) {
            enterFullscreen();
          }
          break;
        case 'Escape':
          event.preventDefault();
          if (isFullscreen) {
            exitFullscreen();
          }
          break;
        case ' ':
          event.preventDefault();
          toggleFullscreen();
          break;
        default:
          break;
      }
    };

    const handleFullscreenChange = () => {
      const isCurrentlyFullscreen = !!(
        document.fullscreenElement ||
        document.webkitFullscreenElement ||
        document.msFullscreenElement
      );
      setIsFullscreen(isCurrentlyFullscreen);
    };

    document.addEventListener('keydown', handleKeyDown);
    document.addEventListener('fullscreenchange', handleFullscreenChange);
    document.addEventListener('webkitfullscreenchange', handleFullscreenChange);
    document.addEventListener('msfullscreenchange', handleFullscreenChange);

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      document.removeEventListener('fullscreenchange', handleFullscreenChange);
      document.removeEventListener('webkitfullscreenchange', handleFullscreenChange);
      document.removeEventListener('msfullscreenchange', handleFullscreenChange);
    };
  }, [isFullscreen, enterFullscreen, exitFullscreen, toggleFullscreen]);

  // 全屏时的渲染
  if (isFullscreen) {
    return (
      <div
        className="fixed inset-0 z-50 cursor-pointer flex items-center justify-center"
        style={{ backgroundColor: colorValue }}
        onClick={toggleFullscreen}
      >
        {/* 3秒提示 */}
        {showFullscreenTip && (
          <div className="text-lg text-center" style={{ color: getTextColor(colorValue) }}>
            {t('Press F for fullscreen, ESC to exit, SPACE to toggle')}
          </div>
        )}
      </div>
    );
  }

  return (
    <div className="text-center pt-10 pb-2">
      <h1 className="text-5xl font-bold text-primary mb-2">
        {title}
      </h1>
      <div className="grid lg:grid-cols-2 gap-8 my-12">
        <Card className="w-full">
          <CardHeader className='px-6'>
            <h3 className="text-lg font-semibold">{t('Screen Preview')}</h3>
          </CardHeader>
          <CardBody className='px-6'>
            <div
              className="aspect-video rounded-lg cursor-pointer border-2 border-gray-300 dark:border-gray-700 transition-all duration-300 hover:scale-[1.02] hover:border-black dark:hover:border-white relative overflow-hidden group"
              style={{ backgroundColor: colorValue }}
              onClick={enterFullscreen}
              onMouseEnter={() => setIsHovered(true)}
              onMouseLeave={() => setIsHovered(false)}
            >
              {/* 颜色值显示在右上角 */}
              <div className="absolute top-2 right-2 text-xs text-gray-700 dark:text-gray-300 bg-white/80 dark:bg-black/80 px-2 py-1 rounded">
                {colorValue}
              </div>

              {/* 鼠标悬停时显示提示 */}
              {isHovered && (
                <div className="absolute inset-0 flex items-center justify-center bg-black/20">
                  <div className="text-sm" style={{ color: getTextColor(colorValue) }}>
                    {t('Click to enter full screen')}
                  </div>
                </div>
              )}
            </div>
          </CardBody>
          <CardFooter className='flex justify-center items-center'>
            <p className='text-sm text-gray-500 mb-2'>{t('Press F for fullscreen, ESC to exit, SPACE to toggle')}</p>
          </CardFooter>
        </Card>
        <div className='w-full'>
          <Card>
            <CardHeader className='px-6'>
              <h3 className="text-lg font-semibold">{t('Color Selection')}</h3>
            </CardHeader>
            <CardBody>
              <div className='flex items-center gap-4 flex-wrap'>
                {Object.keys(colorDatas).map((colorKey) => (
                  <Button
                    key={colorKey}
                    href={`${colorDatas[colorKey].path}`}
                    as={Link}
                    className={clsx('border-2 border-gray-300 min-w-[90px]', colorDatas[colorKey].labelTextColor)}
                    style={{
                      backgroundColor: colorDatas[colorKey].colorValue
                    }}
                  >
                    {colorDatas[colorKey].label}
                  </Button>
                ))}
                <div className="relative min-w-[90px] h-10 rounded-medium cursor-pointer overflow-hidden">
                  <input
                    type="color"
                    value={colorValue}
                    onChange={(e) => handleColorInputChange(e.target.value)}
                    className="absolute inset-0 w-full h-full cursor-pointer z-10"
                    style={{
                      border: 'none',
                      outline: 'none',
                      WebkitAppearance: 'none',
                      MozAppearance: 'none',
                      appearance: 'none',
                      opacity: 0.01
                    }}
                  />
                  <div
                    className="absolute inset-0 w-full h-full rounded-medium pointer-events-none flex items-center justify-center"
                    style={{ backgroundColor: colorValue }}
                  >
                    <span
                      className="text-xs font-mono"
                      style={{ color: getTextColor(colorValue) }}
                    >
                      {colorValue}
                    </span>
                  </div>
                </div>
              </div>
            </CardBody>
          </Card>
          <Card className="mt-6">
            <CardHeader className='px-6'>
              <h3 className="text-lg font-semibold">{t('Download Image')}</h3>
            </CardHeader>
            <CardBody className='px-6'>
            </CardBody>
          </Card>
        </div>
      </div>
      <div className="grid lg:grid-cols-2 gap-8 mb-12">
        <Card className="w-full">
          <CardHeader className='px-6'>
            <h3 className="text-lg font-semibold">{t('Timer Settings')}</h3>
          </CardHeader>
          <CardBody className='px-6'>
            <div className='aspect-video rounded-lg cursor-pointer border-2 border-gray-300 dark:border-gray-700 transition-all duration-300 hover:scale-[1.02] hover:border-black dark:hover:border-white relative overflow-hidden group'></div>
          </CardBody>
          <CardFooter></CardFooter>
        </Card>
        <Card className="w-full">
          <CardHeader className='px-6'>
            <h3 className="text-lg font-semibold">{t('OLED Protection Settings')}</h3>
          </CardHeader>
          <CardBody className='px-6'>
            <div className='aspect-video rounded-lg cursor-pointer border-2 border-gray-300 dark:border-gray-700 transition-all duration-300 hover:scale-[1.02] hover:border-black dark:hover:border-white relative overflow-hidden group'></div>
          </CardBody>
          <CardFooter></CardFooter>
        </Card>
      </div>
    </div >
  );
}
